import {useEffect} from 'react';
import styled from '@emotion/styled';
import {Flex, Tooltip} from 'antd';
import {setCurrentAgentId} from '@/regions/staff/agent';
import {useCurrentUser} from '@/regions/user/currentUser';
import {MCPSquareLink} from '@/links/mcp';
import SvgMcpPlaygroundWorkspaceBtn from '@/assets/mcp/McpPlaygroundWorkspaceBtn';
import {HistoryPanel} from './HistoryPanel';
import AgentPanel from './AgentPanel';

const Container = styled.div`
    height: 100vh;
    width: 100%;
    overflow: auto;
    ::-webkit-scrollbar {
        display: none;
    }
`;

const Content = styled.div`
    display: flex;
    flex-direction: column;
    padding: 80px 44px 24px;
    gap: 40px;
    overflow: hidden;
`;

const Greeting = styled.div`
    display: flex;
    align-items: baseline;
    gap: 12px;
`;

const HiText = styled.div`
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 20px;
    line-height: 38px;
    letter-spacing: 0px;
`;

const NameText = styled.div`
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 34px;
    line-height: 38px;
    letter-spacing: 0px;
    background: linear-gradient(
        262.56deg,
        #895feb 5.77%,
        #3258f0 43.42%,
        #040660 79.59%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
`;

const ToolsText = styled.span`
    margin-right: 12px;
    color: #8f8f8f;
`;

const TopBar = styled(Flex)`
    justify-content: space-between;
    align-items: center;
`;

const StaffWorkspace = () => {
    const userInfo = useCurrentUser();

    useEffect(
        () => {
            setCurrentAgentId(0);
        },
        []
    );

    return (
        <Container>
            <Content>
                <TopBar>
                    <Greeting>
                        <NameText>{userInfo?.chineseName}</NameText>
                        <HiText>欢迎来到百度研发数字员工工作台！</HiText>
                    </Greeting>

                    <Flex align="center">
                        <ToolsText>看看还有哪些AI工具🤔</ToolsText>
                        <MCPSquareLink style={{display: 'flex', alignItems: 'center'}}>
                            <Tooltip title="试试打造属于你自己的数字员工">
                                <SvgMcpPlaygroundWorkspaceBtn />
                            </Tooltip>
                        </MCPSquareLink>
                    </Flex>
                </TopBar>
                <AgentPanel />
                <HistoryPanel />
            </Content>
        </Container>
    );
};

export default StaffWorkspace;
