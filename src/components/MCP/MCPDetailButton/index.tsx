import {Button, ButtonProps} from '@panda-design/components';
import {useC<PERSON>back, MouseEvent, CSSProperties} from 'react';
import {useNavigate} from 'react-router-dom';
import {IconDetail} from '@/icons/mcp';
import {MCPSpaceDetailLink} from '@/links/mcp';
import {useMCPWorkspaceId} from '../hooks';

interface Props {
    serverId: number;
    size?: ButtonProps['size'];
    style?: CSSProperties;
}

export const MCPDetailButton = ({serverId, size, style}: Props) => {
    const spaceId = useMCPWorkspaceId();
    const navigate = useNavigate();
    const handleClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(MCPSpaceDetailLink.toUrl({mcpId: serverId, workspaceId: spaceId}));
        },
        [navigate, serverId, spaceId]
    );

    return (
        <Button
            style={{padding: 0, gap: 3, ...style}}
            onClick={handleClick}
            icon={<IconDetail />}
            type="text"
            size={size}
        >
            详情
        </Button>
    );
};
